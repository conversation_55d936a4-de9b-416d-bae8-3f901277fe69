import { useState, useEffect } from "react";
import { BrowserRouter } from "react-router-dom";

import { Contact, Experience, Feedbacks, Hero, Navbar, Works, StarsCanvas } from "./components";
import Footer from "./components/Footer";
import AboutMeSection from "./components/AboutMeSection";
import SkillsShowcase from "./components/SkillsShowcase";
import LoadingScreen from "./components/LoadingScreen";

const App = () => {
  const [isLoading, setIsLoading] = useState(true);

  const handleLoadingComplete = () => {
    setIsLoading(false);
  };

  return (
    <BrowserRouter>
      {isLoading && <LoadingScreen onLoadingComplete={handleLoadingComplete} />}

      <div className='relative z-0 bg-primary'>
        <div className='bg-hero-pattern bg-cover bg-no-repeat bg-center'>
          <Navbar />
          <Hero />
        </div>
        <AboutMeSection />
        <SkillsShowcase />
        <Experience />
        <Works />
        <Feedbacks />
        <div className='relative z-0'>
          <Contact />
          <StarsCanvas />
          <Footer/>
        </div>
      </div>
    </BrowserRouter>
  );
}

export default App;

{"name": "3dfolio", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint src --ext .js,.jsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint src --ext .js,.jsx --fix", "build:analyze": "vite build --mode analyze", "build:production": "NODE_ENV=production vite build", "serve": "vite preview --port 4173", "clean": "rm -rf dist"}, "dependencies": {"@emailjs/browser": "^3.12.1", "@gsap/react": "^2.1.2", "@heroicons/react": "^2.2.0", "@react-three/drei": "^9.56.24", "@react-three/fiber": "^8.11.1", "email-js": "^2.0.3", "framer-motion": "^9.0.7", "gsap": "^3.12.7", "maath": "^0.5.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-icons": "^5.4.0", "react-router-dom": "^6.8.1", "react-tilt": "^1.0.2", "react-vertical-timeline-component": "^3.6.0", "three": "^0.172.0", "vanilla-tilt": "^1.8.1"}, "devDependencies": {"@types/react": "^18.0.27", "@types/react-dom": "^18.0.10", "@vitejs/plugin-react": "^3.1.0", "autoprefixer": "^10.4.13", "postcss": "^8.4.21", "tailwindcss": "^3.2.6", "vite": "^4.1.0"}}
import logo from "./logo.png"; // Temporarily reverting back to original logo
import backend from "./backend.png";
import creator from "./creator.png";
import mobile from "./mobile.png";
import web from "./web.png";
import github from "./github.png";
import menu from "./menu.svg";
import close from "./close.svg";
import externalLink from "./external-link.svg";
import gsap from './tech/gsap.png'
import framer from './tech/framer.png';
import css from "./tech/css.png";
import figma from "./tech/figma.png";
import hezziBlogs from './blogs.jpg';
import project3 from './E-commerce.jpg';
import resumeBuilder from './resume.jpg';
import cryptoAgent from './crypto-agent.jpg';
import agenticWorld from './agentic-world.jpg';
import mysql from './tech/mysql.png';
import express from './tech/express.png';
import aws from './tech/aws.png';
import mui from './tech/mui.png'
import git from "./tech/git.png";
import html from "./tech/html.png";
import javascript from "./tech/javascript.png";
import mongodb from "./tech/mongodb.png";
import nodejs from "./tech/nodejs.png";
import reactjs from "./tech/reactjs.png";
import redux from "./tech/redux.png";
import tailwind from "./tech/tailwind.png";
import threejs from "./tech/threejs.svg";
import firebase from "./tech/firebase.png";
import typescript from "./tech/typescript.png";
import firstTestimonial from './Tars Ken.webp';
import secondTestimonial from './image.webp';
import thirdTestimonial from './third testimonial.webp'

export {
  logo,
  backend,
  creator,
  mobile,
  web,
  github,
  menu,
  close,
  externalLink,
  css,
  hezziBlogs,
  resumeBuilder,
  project3,
  cryptoAgent,
  agenticWorld,
  mysql,
  express,
  aws,
  mui,
  firstTestimonial,
  secondTestimonial,
  thirdTestimonial,
  gsap,
  framer,
  figma,
  git,
  html,
  javascript,
  mongodb,
  nodejs,
  reactjs,
  redux,
  tailwind,
  threejs,
  firebase,
  typescript,
};
